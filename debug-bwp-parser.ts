import { BWPParser } from './src/parsers/bwp-parser';
import * as path from 'path';

async function debugBWPParser() {
  const parser = new BWPParser();
  const bwpFilePath = path.join(__dirname, 'test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');
  
  try {
    console.log('Parsing BWP file:', bwpFilePath);
    const parsedProcess = await parser.parseBWP(bwpFilePath);
    console.log('Successfully parsed BWP file');
    console.log('Process name:', parsedProcess.name);
    console.log('Namespace:', parsedProcess.namespace);
    console.log('Variables count:', parsedProcess.variables.length);
    console.log('Partner links count:', parsedProcess.partnerLinks.length);
    console.log('REST endpoints count:', parsedProcess.restEndpoints.length);
    console.log('Activities count:', parsedProcess.activities.length);
  } catch (error) {
    console.error('Error parsing BWP file:', error);
  }
}

debugBWPParser();
