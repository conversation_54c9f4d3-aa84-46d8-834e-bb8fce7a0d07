import * as fs from 'fs';
import * as path from 'path';
import { JavaGenerationOptions } from '../types';

/**
 * Spring Boot 项目部署器
 * 负责将生成的 Java 代码复制到 Spring Boot 项目中
 */
export class SpringBootDeployer {
  private springBootProjectPath: string;
  private options: JavaGenerationOptions;

  constructor(springBootProjectPath: string, options: JavaGenerationOptions) {
    this.springBootProjectPath = springBootProjectPath;
    this.options = options;
  }

  /**
   * 部署生成的 Java 代码到 Spring Boot 项目
   */
  async deployGeneratedCode(generatedCodePath: string): Promise<void> {
    console.log(`Deploying generated code from ${generatedCodePath} to ${this.springBootProjectPath}`);

    // 验证 Spring Boot 项目路径
    await this.validateSpringBootProject();

    // 复制生成的 Java 文件
    await this.copyJavaFiles(generatedCodePath);

    // 更新 pom.xml 或 build.gradle（如果需要）
    await this.updateBuildConfiguration();

    console.log('Deployment completed successfully');
  }

  /**
   * 验证 Spring Boot 项目结构
   */
  private async validateSpringBootProject(): Promise<void> {
    if (!fs.existsSync(this.springBootProjectPath)) {
      throw new Error(`Spring Boot project path does not exist: ${this.springBootProjectPath}`);
    }

    // 检查是否是 Maven 项目
    const pomPath = path.join(this.springBootProjectPath, 'pom.xml');
    const gradlePath = path.join(this.springBootProjectPath, 'build.gradle');

    if (!fs.existsSync(pomPath) && !fs.existsSync(gradlePath)) {
      throw new Error('Not a valid Spring Boot project: missing pom.xml or build.gradle');
    }

    // 检查 src/main/java 目录
    const srcMainJavaPath = path.join(this.springBootProjectPath, 'src', 'main', 'java');
    if (!fs.existsSync(srcMainJavaPath)) {
      console.log('Creating src/main/java directory...');
      fs.mkdirSync(srcMainJavaPath, { recursive: true });
    }
  }

  /**
   * 复制 Java 文件到 Spring Boot 项目
   */
  private async copyJavaFiles(generatedCodePath: string): Promise<void> {
    const srcMainJavaPath = path.join(this.springBootProjectPath, 'src', 'main', 'java');
    
    // 复制所有生成的 Java 文件
    await this.copyDirectoryRecursive(generatedCodePath, srcMainJavaPath);
  }

  /**
   * 递归复制目录
   */
  private async copyDirectoryRecursive(source: string, destination: string): Promise<void> {
    if (!fs.existsSync(source)) {
      console.warn(`Source directory does not exist: ${source}`);
      return;
    }

    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    const items = fs.readdirSync(source);

    for (const item of items) {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      const stat = fs.statSync(sourcePath);

      if (stat.isDirectory()) {
        await this.copyDirectoryRecursive(sourcePath, destPath);
      } else if (stat.isFile() && item.endsWith('.java')) {
        console.log(`Copying ${sourcePath} to ${destPath}`);
        fs.copyFileSync(sourcePath, destPath);
      }
    }
  }

  /**
   * 更新构建配置（添加必要的依赖）
   */
  private async updateBuildConfiguration(): Promise<void> {
    const pomPath = path.join(this.springBootProjectPath, 'pom.xml');
    
    if (fs.existsSync(pomPath)) {
      await this.updatePomXml(pomPath);
    }
  }

  /**
   * 更新 pom.xml 文件，添加必要的依赖
   */
  private async updatePomXml(pomPath: string): Promise<void> {
    const pomContent = fs.readFileSync(pomPath, 'utf-8');
    
    // 检查是否已经包含必要的依赖
    const requiredDependencies = [
      'spring-boot-starter-web',
      'spring-boot-starter-validation',
      'jackson-annotations'
    ];

    let needsUpdate = false;
    const missingDependencies: string[] = [];

    for (const dep of requiredDependencies) {
      if (!pomContent.includes(dep)) {
        missingDependencies.push(dep);
        needsUpdate = true;
      }
    }

    if (needsUpdate) {
      console.log('Adding missing dependencies to pom.xml:', missingDependencies);
      await this.addDependenciesToPom(pomPath, pomContent, missingDependencies);
    } else {
      console.log('All required dependencies are already present in pom.xml');
    }
  }

  /**
   * 添加依赖到 pom.xml
   */
  private async addDependenciesToPom(pomPath: string, pomContent: string, dependencies: string[]): Promise<void> {
    let updatedContent = pomContent;

    // 查找 dependencies 标签
    const dependenciesRegex = /(<dependencies>)([\s\S]*?)(<\/dependencies>)/;
    const match = updatedContent.match(dependenciesRegex);

    if (match) {
      const dependenciesSection = match[2];
      let newDependencies = '';

      for (const dep of dependencies) {
        switch (dep) {
          case 'spring-boot-starter-web':
            if (!dependenciesSection.includes('spring-boot-starter-web')) {
              newDependencies += `
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>`;
            }
            break;
          case 'spring-boot-starter-validation':
            if (!dependenciesSection.includes('spring-boot-starter-validation')) {
              newDependencies += `
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>`;
            }
            break;
          case 'jackson-annotations':
            if (!dependenciesSection.includes('jackson-annotations')) {
              newDependencies += `
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>`;
            }
            break;
        }
      }

      if (newDependencies) {
        updatedContent = updatedContent.replace(
          dependenciesRegex,
          `$1$2${newDependencies}
    $3`
        );

        // 备份原文件
        fs.copyFileSync(pomPath, `${pomPath}.backup`);
        
        // 写入更新的内容
        fs.writeFileSync(pomPath, updatedContent, 'utf-8');
        console.log('Updated pom.xml with new dependencies');
      }
    } else {
      console.warn('Could not find dependencies section in pom.xml');
    }
  }

  /**
   * 验证部署是否成功
   */
  async validateDeployment(): Promise<boolean> {
    try {
      const srcMainJavaPath = path.join(this.springBootProjectPath, 'src', 'main', 'java');
      const packagePath = path.join(srcMainJavaPath, ...this.options.packageName.split('.'));
      
      // 检查是否有 Java 文件被复制
      if (fs.existsSync(packagePath)) {
        const files = fs.readdirSync(packagePath, { recursive: true });
        const javaFiles = files.filter(file => typeof file === 'string' && file.endsWith('.java'));
        
        if (javaFiles.length > 0) {
          console.log(`Deployment validation successful: ${javaFiles.length} Java files found`);
          return true;
        }
      }
      
      console.warn('Deployment validation failed: No Java files found in target directory');
      return false;
    } catch (error) {
      console.error('Error during deployment validation:', error);
      return false;
    }
  }

  /**
   * 获取 Spring Boot 项目信息
   */
  getProjectInfo(): { path: string; type: 'maven' | 'gradle' | 'unknown' } {
    const pomPath = path.join(this.springBootProjectPath, 'pom.xml');
    const gradlePath = path.join(this.springBootProjectPath, 'build.gradle');

    let type: 'maven' | 'gradle' | 'unknown' = 'unknown';
    if (fs.existsSync(pomPath)) {
      type = 'maven';
    } else if (fs.existsSync(gradlePath)) {
      type = 'gradle';
    }

    return {
      path: this.springBootProjectPath,
      type
    };
  }
}
