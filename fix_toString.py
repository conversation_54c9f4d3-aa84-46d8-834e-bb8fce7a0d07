#!/usr/bin/env python3
import os
import re
import glob

def fix_toString_method(file_path):
    with open(file_path, 'r') as f:
        content = f.read()

    # Extract class name from file path
    class_name = os.path.basename(file_path).replace('.java', '')

    # Pattern to match broken toString methods - more specific
    pattern = r'(@Override\s+public String toString\(\) \{[^}]*return[^}]*\+[^}]*\})'

    def replace_toString(match):
        return f'''@Override
    public String toString() {{
        return "{class_name}{{" +
                "}}";
    }}'''

    # Replace broken toString methods
    fixed_content = re.sub(pattern, replace_toString, content, flags=re.DOTALL)

    with open(file_path, 'w') as f:
        f.write(fixed_content)

    return content != fixed_content

# Find all Java files in the current directory
java_files = glob.glob('src/main/java/**/*.java', recursive=True)

fixed_count = 0
for java_file in java_files:
    try:
        if fix_toString_method(java_file):
            fixed_count += 1
            print(f"Fixed: {java_file}")
    except Exception as e:
        print(f"Error fixing {java_file}: {e}")

print(f"Done! Fixed {fixed_count} files.")
