package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("HttpFaultHeaders")
@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpFaultHeaders {



    /**
     * Default constructor
     */
    public HttpFaultHeaders() {
    }



    @Override
    public String toString() {
        return "HttpFaultHeaders{" +
                "}";
    }
}
