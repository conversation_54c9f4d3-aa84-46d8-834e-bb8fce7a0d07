package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: /T1563811039923Converted/JsonSchema
 * Root Element
 */
@JsonRootName("OMDBSearchElement")
@JsonIgnoreProperties(ignoreUnknown = true)
public class OMDBSearchElement {



    /**
     * Default constructor
     */
    public OMDBSearchElement() {
    }



    @Override
    public String toString() {
        return "OMDBSearchElement{" +
                "}";
    }
}
