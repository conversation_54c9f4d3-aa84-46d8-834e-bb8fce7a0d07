package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Root Element
 */
@JsonRootName("MovieDetails")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MovieDetails {



    /**
     * Default constructor
     */
    public MovieDetails() {
    }



    @Override
    public String toString() {
        return "MovieDetails{" +
                 +
                "}";
    }
}
