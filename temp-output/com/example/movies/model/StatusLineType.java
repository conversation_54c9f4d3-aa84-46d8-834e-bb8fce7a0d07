package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatusLineType {

    @NotNull
    @JsonProperty("statusCode")
    private Integer statusCode;

    /**
     * Default constructor
     */
    public StatusLineType() {
    }

    /**
     * Parameterized constructor
     */
    public StatusLineType(Integer statusCode) {
        this.statusCode = statusCode;
    }

    /**
     * Get statusCode
     */
    public Integer getStatusCode() {
        return statusCode;
    }

    /**
     * Set statusCode
     */
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String toString() {
        return "StatusLineType{" +
                "statusCode=" + statusCode +
                "}";
    }
}
