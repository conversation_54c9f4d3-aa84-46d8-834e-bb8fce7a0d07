package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Client4XXErrorType {

    @NotNull
    @JsonProperty("statusCode")
    private Integer statusCode;

    @JsonProperty("message")
    private String message;

    /**
     * Default constructor
     */
    public Client4XXErrorType() {
    }

    /**
     * Parameterized constructor
     */
    public Client4XXErrorType(Integer statusCode, String message) {
        this.statusCode = statusCode;
        this.message = message;
    }

    /**
     * Get statusCode
     */
    public Integer getStatusCode() {
        return statusCode;
    }

    /**
     * Set statusCode
     */
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }


    /**
     * Get message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Set message
     */
    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "Client4XXErrorType{" +
                "statusCode=" + statusCode + ", " + "message=" + message +
                "}";
    }
}
