package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: /T1563811039923Converted/JsonSchema
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SearchType {

    @JsonProperty("title")
    private String title;

    @JsonProperty("year")
    private String year;

    @JsonProperty("imdbID")
    private String imdbID;

    @JsonProperty("type")
    private String type;

    @JsonProperty("poster")
    private String poster;

    /**
     * Default constructor
     */
    public SearchType() {
    }

    /**
     * Parameterized constructor
     */
    public SearchType(String title, String year, String imdbID, String type, String poster) {
        this.title = title;
        this.year = year;
        this.imdbID = imdbID;
        this.type = type;
        this.poster = poster;
    }

    /**
     * Get title
     */
    public String getTitle() {
        return title;
    }

    /**
     * Set title
     */
    public void setTitle(String title) {
        this.title = title;
    }


    /**
     * Get year
     */
    public String getYear() {
        return year;
    }

    /**
     * Set year
     */
    public void setYear(String year) {
        this.year = year;
    }


    /**
     * Get imdbID
     */
    public String getImdbID() {
        return imdbID;
    }

    /**
     * Set imdbID
     */
    public void setImdbID(String imdbID) {
        this.imdbID = imdbID;
    }


    /**
     * Get type
     */
    public String getType() {
        return type;
    }

    /**
     * Set type
     */
    public void setType(String type) {
        this.type = type;
    }


    /**
     * Get poster
     */
    public String getPoster() {
        return poster;
    }

    /**
     * Set poster
     */
    public void setPoster(String poster) {
        this.poster = poster;
    }

    @Override
    public String toString() {
        return "SearchType{" +
                "title=" + title + ", " + "year=" + year + ", " + "imdbID=" + imdbID + ", " + "type=" + type + ", " + "poster=" + poster +
                "}";
    }
}
