package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("JwtClaims")
@JsonIgnoreProperties(ignoreUnknown = true)
public class JwtClaims {



    /**
     * Default constructor
     */
    public JwtClaims() {
    }



    @Override
    public String toString() {
        return "JwtClaims{" +
                "}";
    }
}
